/**
 * 🎯 ENHANCED KANBAN BOARD
 *
 * Unified Kanban board that seamlessly integrates both sales pipeline and service workflow
 * with comprehensive customer profile integration and real-time synchronization.
 *
 * Features:
 * - Dual-mode: Sales Pipeline (7 stages) + Service Workflow (8 stages)
 * - Real-time integration with UnifiedCustomerProfileService
 * - AI-powered insights and automation
 * - Cosmic-level mobile UX with responsive design
 * - Comprehensive analytics and reporting
 *
 * Philosophy: "One board to rule them all - from lead to loyal customer"
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Progress } from "~/components/ui/progress";
import { Separator } from "~/components/ui/separator";
import {
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  MessageSquare,
  Wrench,
  FileText,
  BarChart3,
  Brain,
  RefreshCw,
  Filter,
  Plus,
  Users,
  Target,
  Zap,
  Star,
  ArrowRight
} from 'lucide-react';

// Pragmatic DnD imports
import { DragDropContext, Droppable, Draggable } from '@atlaskit/pragmatic-drag-and-drop-react-beautiful-dnd';

export interface EnhancedKanbanBoardProps {
  mode: 'SALES_PIPELINE' | 'SERVICE_WORKFLOW';
  filters?: {
    salesRepId?: string;
    technicianId?: string;
    customerId?: string;
    priority?: string;
    serviceType?: string;
    dateRange?: { start: Date; end: Date };
  };
  onCardMove?: (cardId: string, fromStage: string, toStage: string) => Promise<void>;
  onCardClick?: (card: any) => void;
  onModeChange?: (mode: 'SALES_PIPELINE' | 'SERVICE_WORKFLOW') => void;
  showAnalytics?: boolean;
  refreshInterval?: number;
}

export function EnhancedKanbanBoard({
  mode,
  filters,
  onCardMove,
  onCardClick,
  onModeChange,
  showAnalytics = true,
  refreshInterval = 30000 // 30 seconds
}: EnhancedKanbanBoardProps) {
  const [board, setBoard] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [draggedCard, setDraggedCard] = useState<string | null>(null);
  const [analytics, setAnalytics] = useState<any>(null);

  // Load board data based on mode
  const loadBoard = async () => {
    setLoading(true);
    setError(null);

    try {
      const endpoint = mode === 'SALES_PIPELINE' ? '/api/sales-pipeline' : '/api/kanban';
      const params = new URLSearchParams({
        action: 'get-board',
        ...filters,
      });

      const response = await fetch(`${endpoint}?${params}`);
      const data = await response.json();

      if (data.success) {
        setBoard(data.board);
        if (data.analytics) {
          setAnalytics(data.analytics);
        }
      } else {
        setError(data.error || 'Failed to load board');
      }
    } catch (err) {
      console.error('Error loading board:', err);
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Auto-refresh board data
  useEffect(() => {
    loadBoard();

    const interval = setInterval(loadBoard, refreshInterval);
    return () => clearInterval(interval);
  }, [mode, filters, refreshInterval]);

  // Handle drag and drop
  const handleDragEnd = async (result: any) => {
    const { destination, source, draggableId } = result;

    if (!destination || !board) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    const fromStage = source.droppableId;
    const toStage = destination.droppableId;

    setDraggedCard(draggableId);

    // Optimistic update
    const newBoard = { ...board };
    const sourceCards = [...newBoard.stages[fromStage].cards];
    const destCards = fromStage === toStage ? sourceCards : [...newBoard.stages[toStage].cards];

    const [movedCard] = sourceCards.splice(source.index, 1);
    movedCard.stage = toStage;
    destCards.splice(destination.index, 0, movedCard);

    newBoard.stages[fromStage].cards = sourceCards;
    newBoard.stages[toStage].cards = destCards;

    setBoard(newBoard);

    // Call API
    try {
      if (onCardMove) {
        await onCardMove(draggableId, fromStage, toStage);
      } else {
        const endpoint = mode === 'SALES_PIPELINE' ? '/api/sales-pipeline' : '/api/kanban';
        const formData = new FormData();
        formData.append('action', 'move-card');
        formData.append('cardId', draggableId);
        formData.append('fromStage', fromStage);
        formData.append('toStage', toStage);

        await fetch(endpoint, {
          method: 'POST',
          body: formData,
        });
      }
    } catch (error) {
      console.error('Error moving card:', error);
      // Revert optimistic update
      loadBoard();
    }

    setDraggedCard(null);
  };

  // Get stage configuration based on mode
  const getStageConfig = () => {
    if (mode === 'SALES_PIPELINE') {
      return {
        title: 'Sales Pipeline',
        description: '7-stage sales process from lead to retention',
        icon: <Target className="h-5 w-5" />,
        color: 'bg-blue-500',
      };
    } else {
      return {
        title: 'Service Workflow',
        description: '8-stage service delivery process',
        icon: <Wrench className="h-5 w-5" />,
        color: 'bg-green-500',
      };
    }
  };

  const stageConfig = getStageConfig();

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading {stageConfig.title.toLowerCase()}...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !board) {
    return (
      <Card className="border-destructive">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-destructive mb-2">Error Loading Board</h3>
            <p className="text-muted-foreground mb-4">{error || 'Board data not found'}</p>
            <Button onClick={loadBoard} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className={`p-2 rounded-lg ${stageConfig.color} text-white`}>
            {stageConfig.icon}
          </div>
          <div>
            <h1 className="text-2xl font-bold">{stageConfig.title}</h1>
            <p className="text-muted-foreground">{stageConfig.description}</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Mode Toggle */}
          <Tabs value={mode} onValueChange={(value) => onModeChange?.(value as any)}>
            <TabsList>
              <TabsTrigger value="SALES_PIPELINE" className="flex items-center space-x-2">
                <Target className="h-4 w-4" />
                <span>Sales</span>
              </TabsTrigger>
              <TabsTrigger value="SERVICE_WORKFLOW" className="flex items-center space-x-2">
                <Wrench className="h-4 w-4" />
                <span>Service</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Button variant="outline" onClick={loadBoard}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>

          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>

          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add {mode === 'SALES_PIPELINE' ? 'Lead' : 'Service'}
          </Button>
        </div>
      </div>

      {/* Analytics Summary */}
      {showAnalytics && analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {mode === 'SALES_PIPELINE' ? 'Pipeline Value' : 'Active Jobs'}
                  </p>
                  <p className="text-2xl font-bold">
                    {mode === 'SALES_PIPELINE'
                      ? `$${analytics.totalPipelineValue?.toLocaleString() || 0}`
                      : analytics.totalCards || 0
                    }
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {mode === 'SALES_PIPELINE' ? 'Weighted Value' : 'Efficiency'}
                  </p>
                  <p className="text-2xl font-bold">
                    {mode === 'SALES_PIPELINE'
                      ? `$${analytics.weightedPipelineValue?.toLocaleString() || 0}`
                      : `${analytics.efficiency || 0}%`
                    }
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {mode === 'SALES_PIPELINE' ? 'Avg Deal Size' : 'Avg Cycle Time'}
                  </p>
                  <p className="text-2xl font-bold">
                    {mode === 'SALES_PIPELINE'
                      ? `$${analytics.averageDealSize?.toLocaleString() || 0}`
                      : `${analytics.averageCycleTime || 0}h`
                    }
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {mode === 'SALES_PIPELINE' ? 'Sales Cycle' : 'Bottlenecks'}
                  </p>
                  <p className="text-2xl font-bold">
                    {mode === 'SALES_PIPELINE'
                      ? `${analytics.averageSalesCycle || 0}d`
                      : analytics.bottlenecks?.length || 0
                    }
                  </p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Kanban Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {Object.entries(board.stages).map(([stageKey, stage]: [string, any]) => (
            <KanbanColumn
              key={stageKey}
              stageKey={stageKey}
              stage={stage}
              mode={mode}
              onCardClick={onCardClick}
              isDraggedOver={draggedCard !== null}
            />
          ))}
        </div>
      </DragDropContext>
    </div>
  );
}

// Enhanced Kanban Column Component
interface KanbanColumnProps {
  stageKey: string;
  stage: any;
  mode: 'SALES_PIPELINE' | 'SERVICE_WORKFLOW';
  onCardClick?: (card: any) => void;
  isDraggedOver: boolean;
}

function KanbanColumn({ stageKey, stage, mode, onCardClick, isDraggedOver }: KanbanColumnProps) {
  const getStageIcon = () => {
    if (mode === 'SALES_PIPELINE') {
      const icons = {
        NEW_LEAD: '📞',
        QUALIFIED: '✅',
        PROPOSAL: '📄',
        NEGOTIATION: '🤝',
        IN_PROGRESS: '🔧',
        CLOSED_WON: '💰',
        FOLLOW_UP: '🔄',
      };
      return icons[stageKey as keyof typeof icons] || '📋';
    } else {
      const icons = {
        BACKLOG: '📋',
        SCHEDULED: '📅',
        IN_PROGRESS: '🔧',
        PENDING_PARTS: '📦',
        QUALITY_CHECK: '✅',
        COMPLETED: '🎉',
        BILLED: '💰',
        CLOSED: '🔒',
      };
      return icons[stageKey as keyof typeof icons] || '📋';
    }
  };

  const getStageColor = () => {
    if (mode === 'SALES_PIPELINE') {
      const colors = {
        NEW_LEAD: 'border-blue-200 bg-blue-50',
        QUALIFIED: 'border-green-200 bg-green-50',
        PROPOSAL: 'border-yellow-200 bg-yellow-50',
        NEGOTIATION: 'border-orange-200 bg-orange-50',
        IN_PROGRESS: 'border-purple-200 bg-purple-50',
        CLOSED_WON: 'border-green-300 bg-green-100',
        FOLLOW_UP: 'border-indigo-200 bg-indigo-50',
      };
      return colors[stageKey as keyof typeof colors] || 'border-gray-200 bg-gray-50';
    } else {
      const colors = {
        BACKLOG: 'border-gray-200 bg-gray-50',
        SCHEDULED: 'border-blue-200 bg-blue-50',
        IN_PROGRESS: 'border-yellow-200 bg-yellow-50',
        PENDING_PARTS: 'border-orange-200 bg-orange-50',
        QUALITY_CHECK: 'border-purple-200 bg-purple-50',
        COMPLETED: 'border-green-200 bg-green-50',
        BILLED: 'border-green-300 bg-green-100',
        CLOSED: 'border-gray-300 bg-gray-100',
      };
      return colors[stageKey as keyof typeof colors] || 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="flex-shrink-0 w-80">
      <Card className={`h-full ${getStageColor()}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-lg">{getStageIcon()}</span>
              <div>
                <CardTitle className="text-sm font-semibold">{stage.name}</CardTitle>
                <p className="text-xs text-muted-foreground">{stage.description}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="text-xs">
                {stage.cards?.length || 0}
              </Badge>
              {stage.metrics && (
                <Badge variant="outline" className="text-xs">
                  ${stage.metrics.totalValue?.toLocaleString() || 0}
                </Badge>
              )}
            </div>
          </div>

          {/* Stage Metrics */}
          {stage.metrics && (
            <div className="mt-2 space-y-1">
              {mode === 'SALES_PIPELINE' && (
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Conversion Rate</span>
                  <span>{stage.metrics.conversionRate || 0}%</span>
                </div>
              )}
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Avg Time</span>
                <span>{stage.metrics.averageTimeInStage || 0}h</span>
              </div>
            </div>
          )}
        </CardHeader>

        <Droppable droppableId={stageKey}>
          {(provided, snapshot) => (
            <CardContent
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`space-y-3 min-h-96 max-h-96 overflow-y-auto ${
                snapshot.isDraggedOver ? 'bg-blue-50' : ''
              }`}
            >
              {stage.cards?.map((card: any, index: number) => (
                <EnhancedKanbanCard
                  key={card.id}
                  card={card}
                  index={index}
                  mode={mode}
                  onClick={() => onCardClick?.(card)}
                />
              ))}
              {provided.placeholder}

              {(!stage.cards || stage.cards.length === 0) && (
                <div className="text-center py-8 text-gray-400">
                  <div className="text-4xl mb-2">{getStageIcon()}</div>
                  <p className="text-sm">No {mode === 'SALES_PIPELINE' ? 'leads' : 'cards'} in this stage</p>
                </div>
              )}
            </CardContent>
          )}
        </Droppable>
      </Card>
    </div>
  );
}

// Enhanced Kanban Card Component
interface EnhancedKanbanCardProps {
  card: any;
  index: number;
  mode: 'SALES_PIPELINE' | 'SERVICE_WORKFLOW';
  onClick?: () => void;
}

function EnhancedKanbanCard({ card, index, mode, onClick }: EnhancedKanbanCardProps) {
  const getPriorityColor = (priority: string) => {
    const colors = {
      URGENT: 'destructive',
      HIGH: 'default',
      MEDIUM: 'secondary',
      LOW: 'outline',
    };
    return colors[priority as keyof typeof colors] || 'outline';
  };

  const getServiceTypeIcon = (serviceType: string) => {
    const icons = {
      INSTALLATION: '🔧',
      REPAIR: '🛠️',
      MAINTENANCE: '⚙️',
      INSPECTION: '🔍',
      CONSULTATION: '💬',
    };
    return icons[serviceType as keyof typeof icons] || '🔧';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Draggable draggableId={card.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`bg-white border rounded-lg p-3 cursor-pointer hover:shadow-md transition-shadow ${
            snapshot.isDragging ? 'shadow-lg rotate-2' : ''
          }`}
          onClick={onClick}
        >
          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getServiceTypeIcon(card.serviceType)}</span>
                <Badge variant={getPriorityColor(card.priority)} className="text-xs">
                  {card.priority}
                </Badge>
              </div>
              <span className="text-xs text-gray-500">
                #{card.id.slice(-6)}
              </span>
            </div>

            {/* Title and Description */}
            <div>
              <h4 className="font-medium text-sm line-clamp-2">{card.title}</h4>
              {card.description && (
                <p className="text-xs text-gray-600 mt-1 line-clamp-2">{card.description}</p>
              )}
            </div>

            {/* Customer Info */}
            <div className="space-y-1">
              <div className="flex items-center space-x-2 text-xs">
                <Users className="h-3 w-3 text-gray-400" />
                <span className="font-medium">
                  {mode === 'SALES_PIPELINE' ? card.contactInfo?.name : card.customer?.name}
                </span>
              </div>
              {(card.contactInfo?.phone || card.customer?.phone) && (
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  <Phone className="h-3 w-3 text-gray-400" />
                  <span>{card.contactInfo?.phone || card.customer?.phone}</span>
                </div>
              )}
            </div>

            {/* Sales Pipeline Specific Info */}
            {mode === 'SALES_PIPELINE' && (
              <div className="space-y-2">
                {card.estimatedValue && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Value:</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrency(card.estimatedValue)}
                    </span>
                  </div>
                )}

                {card.probability && (
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600">Probability:</span>
                      <span className="font-semibold">{card.probability}%</span>
                    </div>
                    <Progress value={card.probability} className="h-1" />
                  </div>
                )}

                {card.leadScore && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Lead Score:</span>
                    <div className="flex items-center space-x-1">
                      <Star className="h-3 w-3 text-yellow-500" />
                      <span className="font-semibold">{card.leadScore}/100</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Service Workflow Specific Info */}
            {mode === 'SERVICE_WORKFLOW' && (
              <div className="space-y-2">
                {card.scheduledDate && (
                  <div className="flex items-center space-x-2 text-xs text-gray-600">
                    <Calendar className="h-3 w-3 text-gray-400" />
                    <span>{new Date(card.scheduledDate).toLocaleDateString()}</span>
                  </div>
                )}

                {card.assignedTechnician && (
                  <div className="flex items-center space-x-2 text-xs">
                    <Avatar className="h-4 w-4">
                      <AvatarFallback className="text-xs">
                        {card.assignedTechnician.name?.charAt(0) || 'T'}
                      </AvatarFallback>
                    </Avatar>
                    <span>{card.assignedTechnician.name}</span>
                  </div>
                )}
              </div>
            )}

            {/* AI Insights */}
            {card.aiInsights && (
              <div className="pt-2 border-t">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-1">
                    <Brain className="h-3 w-3 text-purple-500" />
                    <span className="text-gray-600">AI Insights</span>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {card.aiInsights.engagementLevel}
                  </Badge>
                </div>

                {card.aiInsights.recommendedActions?.length > 0 && (
                  <div className="mt-1">
                    <p className="text-xs text-gray-600 line-clamp-1">
                      💡 {card.aiInsights.recommendedActions[0]}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>
                {new Date(card.updatedAt).toLocaleDateString()}
              </span>
              {card.unifiedProfileData && (
                <div className="flex items-center space-x-1">
                  <Zap className="h-3 w-3 text-blue-500" />
                  <span>Unified</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );
}
