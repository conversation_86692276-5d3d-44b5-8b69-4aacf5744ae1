/**
 * 🎯 ENHANCED SALES PIPELINE SERVICE
 *
 * Comprehensive 7-stage sales pipeline with automated workflows that integrates
 * seamlessly with the unified customer profile system and existing Kanban infrastructure.
 *
 * Features:
 * - 7-stage sales pipeline from lead to retention
 * - Real-time integration with UnifiedCustomerProfileService
 * - AI-powered lead scoring using Bielik V3
 * - Automated workflows and notifications
 * - Email intelligence integration
 * - Financial dashboard synchronization
 * - Mobile-responsive with cosmic-level UX
 *
 * Philosophy: "Transform every lead into a loyal customer through intelligent automation"
 */

import { prisma } from '~/db.server';
import { UnifiedCustomerProfileService } from './unified-customer-profile.server';
import { bielikService } from './bielik.server';
import { goBackendBridge } from './gobackend-bridge.server';
import { advancedKanbanService } from './advanced-kanban.server';

// 🎯 Sales Pipeline Stages
export type SalesPipelineStage =
  | 'NEW_LEAD'           // Stage 1: Customer calls/emails
  | 'QUALIFIED'          // Stage 2: Interested customer
  | 'PROPOSAL'           // Stage 3: Proposal preparation
  | 'NEGOTIATION'        // Stage 4: Appointment scheduling
  | 'IN_PROGRESS'        // Stage 5: Service execution
  | 'CLOSED_WON'         // Stage 6: Completion & invoicing
  | 'FOLLOW_UP';         // Stage 7: Results analysis & retention

// 📊 Sales Pipeline Card Interface
export interface SalesPipelineCard {
  id: string;
  leadId: string;
  customerId?: string;
  stage: SalesPipelineStage;
  title: string;
  description: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  serviceType: 'INSTALLATION' | 'REPAIR' | 'MAINTENANCE' | 'INSPECTION' | 'CONSULTATION';

  // Lead Information
  leadSource: 'PHONE' | 'EMAIL' | 'WEBSITE' | 'REFERRAL' | 'SOCIAL_MEDIA' | 'WALK_IN';
  contactInfo: {
    name: string;
    phone?: string;
    email?: string;
    address?: string;
    preferredContactMethod: 'EMAIL' | 'PHONE' | 'SMS';
  };

  // Sales Information
  estimatedValue: number;
  probability: number; // 0-100%
  expectedCloseDate?: Date;
  actualCloseDate?: Date;

  // Assignment & Scheduling
  assignedSalesRep?: string;
  assignedTechnician?: string;
  scheduledDate?: Date;
  appointmentConfirmed: boolean;

  // AI & Analytics
  leadScore: number; // 0-100
  aiInsights: {
    churnRisk: number;
    upsellPotential: number;
    sentimentScore: number;
    engagementLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    recommendedActions: string[];
  };

  // Workflow Tracking
  stageHistory: Array<{
    stage: SalesPipelineStage;
    timestamp: Date;
    userId: string;
    notes?: string;
    duration?: number; // time in stage (hours)
  }>;

  // Integration Data
  unifiedProfileData?: any; // From UnifiedCustomerProfileService
  emailIntelligence?: any;  // From email system
  financialData?: any;      // From financial dashboard

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    systemType?: string;
    urgencyLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'EMERGENCY';
    specialRequirements?: string[];
    proposalSent?: boolean;
    proposalViewed?: boolean;
    contractSigned?: boolean;
    invoiceGenerated?: boolean;
    paymentReceived?: boolean;
    satisfactionSurveyCompleted?: boolean;
    followUpScheduled?: boolean;
  };
}

// 🏗️ Sales Pipeline Board
export interface SalesPipelineBoard {
  stages: Record<SalesPipelineStage, {
    name: string;
    description: string;
    color: string;
    icon: string;
    cards: SalesPipelineCard[];
    automation: {
      autoAdvanceConditions?: string[];
      requiredFields?: string[];
      notifications?: string[];
      aiTriggers?: string[];
    };
    metrics: {
      averageTimeInStage: number; // hours
      conversionRate: number;     // percentage to next stage
      totalValue: number;         // sum of estimated values
      cardCount: number;
    };
  }>;
  analytics: {
    totalPipelineValue: number;
    weightedPipelineValue: number;
    averageDealSize: number;
    conversionRates: Record<SalesPipelineStage, number>;
    averageSalesCycle: number; // days
    topPerformers: Array<{ name: string; value: number }>;
  };
}

/**
 * 🚀 ENHANCED SALES PIPELINE SERVICE
 *
 * Main service class that orchestrates the 7-stage sales pipeline with
 * comprehensive integration to unified customer profiles and AI insights.
 */
export class EnhancedSalesPipelineService {

  // Stage definitions with automation rules
  private static stageDefinitions: Record<SalesPipelineStage, any> = {
    NEW_LEAD: {
      name: 'New Lead',
      description: 'Customer calls/emails - Initial contact',
      color: 'bg-blue-500',
      icon: '📞',
      automation: {
        autoAdvanceConditions: ['lead_score >= 70', 'contact_info_complete'],
        requiredFields: ['name', 'phone_or_email', 'service_type'],
        notifications: ['sales_rep_assignment', 'welcome_email'],
        aiTriggers: ['calculate_lead_score', 'sentiment_analysis']
      }
    },
    QUALIFIED: {
      name: 'Qualified',
      description: 'Interested customer - Data collection',
      color: 'bg-green-500',
      icon: '✅',
      automation: {
        autoAdvanceConditions: ['basic_data_collected', 'urgency_assessed'],
        requiredFields: ['system_type', 'address', 'urgency_level'],
        notifications: ['proposal_preparation_alert'],
        aiTriggers: ['upsell_analysis', 'pricing_optimization']
      }
    },
    PROPOSAL: {
      name: 'Proposal',
      description: 'Proposal preparation and approval',
      color: 'bg-yellow-500',
      icon: '📄',
      automation: {
        autoAdvanceConditions: ['proposal_approved', 'proposal_sent'],
        requiredFields: ['estimated_value', 'proposal_document'],
        notifications: ['customer_proposal_sent', 'follow_up_reminder'],
        aiTriggers: ['proposal_optimization', 'timing_analysis']
      }
    },
    NEGOTIATION: {
      name: 'Negotiation',
      description: 'Appointment scheduling and negotiation',
      color: 'bg-orange-500',
      icon: '🤝',
      automation: {
        autoAdvanceConditions: ['appointment_scheduled', 'proposal_accepted'],
        requiredFields: ['scheduled_date', 'technician_assigned'],
        notifications: ['appointment_confirmation', 'technician_assignment'],
        aiTriggers: ['availability_optimization', 'resource_allocation']
      }
    },
    IN_PROGRESS: {
      name: 'In Progress',
      description: 'Service execution and delivery',
      color: 'bg-purple-500',
      icon: '🔧',
      automation: {
        autoAdvanceConditions: ['service_completed', 'quality_check_passed'],
        requiredFields: ['service_report', 'parts_used', 'completion_photos'],
        notifications: ['service_updates', 'completion_alert'],
        aiTriggers: ['quality_assessment', 'completion_prediction']
      }
    },
    CLOSED_WON: {
      name: 'Closed Won',
      description: 'Completion, invoicing, and payment',
      color: 'bg-green-600',
      icon: '💰',
      automation: {
        autoAdvanceConditions: ['invoice_generated', 'payment_received'],
        requiredFields: ['completion_signature', 'invoice_sent'],
        notifications: ['invoice_sent', 'payment_reminder'],
        aiTriggers: ['satisfaction_prediction', 'retention_analysis']
      }
    },
    FOLLOW_UP: {
      name: 'Follow-up',
      description: 'Results analysis and retention',
      color: 'bg-indigo-500',
      icon: '🔄',
      automation: {
        autoAdvanceConditions: ['satisfaction_survey_completed', 'follow_up_scheduled'],
        requiredFields: ['satisfaction_score', 'retention_plan'],
        notifications: ['survey_request', 'upsell_opportunities'],
        aiTriggers: ['retention_scoring', 'upsell_identification']
      }
    }
  };

  /**
   * 📊 Get complete sales pipeline board
   */
  static async getSalesPipelineBoard(filters?: {
    salesRepId?: string;
    customerId?: string;
    dateRange?: { start: Date; end: Date };
    serviceType?: string;
    leadSource?: string;
  }): Promise<SalesPipelineBoard> {
    try {
      console.log('🎯 Fetching sales pipeline board with filters:', filters);

      // Build where clause for filtering
      const whereClause: any = {};

      if (filters?.salesRepId) {
        whereClause.assignedSalesRep = filters.salesRepId;
      }

      if (filters?.customerId) {
        whereClause.customerId = filters.customerId;
      }

      if (filters?.serviceType) {
        whereClause.serviceType = filters.serviceType;
      }

      if (filters?.leadSource) {
        whereClause.leadSource = filters.leadSource;
      }

      if (filters?.dateRange) {
        whereClause.createdAt = {
          gte: filters.dateRange.start,
          lte: filters.dateRange.end,
        };
      }

      // Fetch leads/opportunities from database
      const leads = await prisma.lead.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              address: true,
            },
          },
          serviceOrders: {
            include: {
              invoices: true,
              serviceReports: true,
            },
          },
          communications: {
            orderBy: { timestamp: 'desc' },
            take: 5,
          },
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' },
        ],
      });

      // Initialize stages
      const stages: Record<SalesPipelineStage, any> = {} as any;

      Object.entries(this.stageDefinitions).forEach(([stage, definition]) => {
        stages[stage as SalesPipelineStage] = {
          ...definition,
          cards: [],
          metrics: {
            averageTimeInStage: 0,
            conversionRate: 0,
            totalValue: 0,
            cardCount: 0,
          },
        };
      });

      // Process leads into pipeline cards with AI enhancement
      const pipelineCards: SalesPipelineCard[] = [];

      for (const lead of leads) {
        // Get unified customer profile data if customer exists
        let unifiedProfileData = null;
        if (lead.customerId) {
          try {
            unifiedProfileData = await UnifiedCustomerProfileService.getUnifiedProfile(lead.customerId);
          } catch (error) {
            console.warn(`Could not fetch unified profile for customer ${lead.customerId}:`, error);
          }
        }

        // Calculate AI insights
        const aiInsights = await this.calculateAIInsights(lead, unifiedProfileData);

        // Create pipeline card
        const card: SalesPipelineCard = {
          id: lead.id,
          leadId: lead.id,
          customerId: lead.customerId || undefined,
          stage: (lead.stage as SalesPipelineStage) || 'NEW_LEAD',
          title: lead.title || `${lead.serviceType} - ${lead.contactName}`,
          description: lead.description || '',
          priority: lead.priority as any,
          serviceType: lead.serviceType as any,
          leadSource: lead.source as any,
          contactInfo: {
            name: lead.contactName,
            phone: lead.contactPhone,
            email: lead.contactEmail,
            address: lead.contactAddress,
            preferredContactMethod: lead.preferredContactMethod as any || 'EMAIL',
          },
          estimatedValue: lead.estimatedValue || 0,
          probability: lead.probability || 50,
          expectedCloseDate: lead.expectedCloseDate,
          actualCloseDate: lead.actualCloseDate,
          assignedSalesRep: lead.assignedSalesRep,
          assignedTechnician: lead.assignedTechnician,
          scheduledDate: lead.scheduledDate,
          appointmentConfirmed: lead.appointmentConfirmed || false,
          leadScore: aiInsights.leadScore,
          aiInsights,
          stageHistory: lead.stageHistory ? JSON.parse(lead.stageHistory) : [],
          unifiedProfileData,
          createdAt: lead.createdAt,
          updatedAt: lead.updatedAt,
          metadata: {
            systemType: (lead.metadata as any)?.systemType,
            urgencyLevel: (lead.metadata as any)?.urgencyLevel,
            specialRequirements: (lead.metadata as any)?.specialRequirements || [],
            proposalSent: (lead.metadata as any)?.proposalSent || false,
            proposalViewed: (lead.metadata as any)?.proposalViewed || false,
            contractSigned: (lead.metadata as any)?.contractSigned || false,
            invoiceGenerated: (lead.metadata as any)?.invoiceGenerated || false,
            paymentReceived: (lead.metadata as any)?.paymentReceived || false,
            satisfactionSurveyCompleted: (lead.metadata as any)?.satisfactionSurveyCompleted || false,
            followUpScheduled: (lead.metadata as any)?.followUpScheduled || false,
          },
        };

        pipelineCards.push(card);

        // Add card to appropriate stage
        const stage = card.stage;
        if (stages[stage]) {
          stages[stage].cards.push(card);
          stages[stage].metrics.cardCount++;
          stages[stage].metrics.totalValue += card.estimatedValue;
        }
      }

      // Calculate stage metrics and analytics
      await this.calculateStageMetrics(stages);
      const analytics = await this.calculatePipelineAnalytics(pipelineCards);

      const board: SalesPipelineBoard = {
        stages,
        analytics,
      };

      console.log('✅ Sales pipeline board generated successfully');
      return board;

    } catch (error) {
      console.error('❌ Error fetching sales pipeline board:', error);
      throw new Error(`Failed to fetch sales pipeline board: ${error.message}`);
    }
  }

  /**
   * 🔄 Move card between pipeline stages with automated workflows
   */
  static async movePipelineCard(
    cardId: string,
    fromStage: SalesPipelineStage,
    toStage: SalesPipelineStage,
    userId: string,
    metadata?: any
  ): Promise<{ success: boolean; message?: string }> {
    try {
      console.log(`🔄 Moving pipeline card ${cardId} from ${fromStage} to ${toStage}`);

      // Validate stage transition
      const isValidTransition = this.validateStageTransition(fromStage, toStage);
      if (!isValidTransition) {
        return { success: false, message: 'Invalid stage transition' };
      }

      // Get current lead data
      const lead = await prisma.lead.findUnique({
        where: { id: cardId },
        include: {
          customer: true,
          serviceOrders: true,
        },
      });

      if (!lead) {
        return { success: false, message: 'Lead not found' };
      }

      // Execute stage-specific automation
      const automationResult = await this.executeStageAutomation(lead, toStage, metadata);
      if (!automationResult.success) {
        return { success: false, message: automationResult.message };
      }

      // Update lead stage and metadata
      const updatedMetadata = {
        ...(lead.metadata as any || {}),
        ...metadata,
        lastStageChange: new Date().toISOString(),
        stageChangedBy: userId,
      };

      // Update stage history
      const stageHistory = lead.stageHistory ? JSON.parse(lead.stageHistory) : [];
      const previousStageEntry = stageHistory.find((entry: any) => entry.stage === fromStage);
      const timeInPreviousStage = previousStageEntry ?
        (Date.now() - new Date(previousStageEntry.timestamp).getTime()) / (1000 * 60 * 60) : 0;

      stageHistory.push({
        stage: toStage,
        timestamp: new Date(),
        userId,
        notes: metadata?.notes,
        duration: timeInPreviousStage,
      });

      // Update lead in database
      await prisma.lead.update({
        where: { id: cardId },
        data: {
          stage: toStage,
          updatedAt: new Date(),
          metadata: updatedMetadata,
          stageHistory: JSON.stringify(stageHistory),
          // Update probability based on stage
          probability: this.getStageDefaultProbability(toStage),
        },
      });

      // Update unified customer profile if customer exists
      if (lead.customerId) {
        await this.updateUnifiedCustomerProfile(lead.customerId, {
          stageChange: {
            from: fromStage,
            to: toStage,
            timestamp: new Date(),
            leadId: cardId,
          },
        });
      }

      // Send notifications
      await this.sendStageTransitionNotifications(cardId, fromStage, toStage, lead);

      // Execute post-transition automation
      await this.executePostTransitionAutomation(lead, toStage, metadata);

      console.log(`✅ Pipeline card moved successfully: ${fromStage} → ${toStage}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error moving pipeline card:', error);
      return { success: false, message: `Failed to move card: ${error.message}` };
    }
  }

  /**
   * 🤖 Calculate AI insights for lead scoring and recommendations
   */
  private static async calculateAIInsights(lead: any, unifiedProfile?: any): Promise<any> {
    try {
      // Prepare data for AI analysis
      const analysisData = {
        leadInfo: {
          source: lead.source,
          serviceType: lead.serviceType,
          estimatedValue: lead.estimatedValue,
          urgencyLevel: (lead.metadata as any)?.urgencyLevel,
          contactMethod: lead.preferredContactMethod,
        },
        customerHistory: unifiedProfile ? {
          totalRevenue: unifiedProfile.analytics?.totalRevenue || 0,
          serviceCount: unifiedProfile.analytics?.totalServiceOrders || 0,
          healthScore: unifiedProfile.healthScore?.overall || 0,
          churnRisk: unifiedProfile.analytics?.churnRisk || 0,
          communicationTrend: unifiedProfile.communications?.sentimentTrend || 'STABLE',
        } : null,
        communicationData: {
          responseTime: lead.communications?.[0]?.responseTime || 0,
          engagementLevel: this.calculateEngagementLevel(lead.communications || []),
          sentimentScore: this.calculateSentimentScore(lead.communications || []),
        },
      };

      // Calculate lead score using AI
      const leadScore = await this.calculateLeadScore(analysisData);

      // Generate AI recommendations
      const recommendations = await this.generateAIRecommendations(analysisData);

      return {
        leadScore,
        churnRisk: analysisData.customerHistory?.churnRisk || this.calculateBasicChurnRisk(analysisData),
        upsellPotential: this.calculateUpsellPotential(analysisData),
        sentimentScore: analysisData.communicationData.sentimentScore,
        engagementLevel: analysisData.communicationData.engagementLevel,
        recommendedActions: recommendations,
      };

    } catch (error) {
      console.error('❌ Error calculating AI insights:', error);
      // Return default values on error
      return {
        leadScore: 50,
        churnRisk: 30,
        upsellPotential: 40,
        sentimentScore: 0,
        engagementLevel: 'MEDIUM',
        recommendedActions: ['Follow up within 24 hours', 'Gather more requirements'],
      };
    }
  }

  /**
   * 🔧 Execute stage-specific automation workflows
   */
  private static async executeStageAutomation(
    lead: any,
    toStage: SalesPipelineStage,
    metadata?: any
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const stageConfig = this.stageDefinitions[toStage];

      switch (toStage) {
        case 'NEW_LEAD':
          return await this.executeNewLeadAutomation(lead, metadata);

        case 'QUALIFIED':
          return await this.executeQualifiedAutomation(lead, metadata);

        case 'PROPOSAL':
          return await this.executeProposalAutomation(lead, metadata);

        case 'NEGOTIATION':
          return await this.executeNegotiationAutomation(lead, metadata);

        case 'IN_PROGRESS':
          return await this.executeInProgressAutomation(lead, metadata);

        case 'CLOSED_WON':
          return await this.executeClosedWonAutomation(lead, metadata);

        case 'FOLLOW_UP':
          return await this.executeFollowUpAutomation(lead, metadata);

        default:
          return { success: true };
      }
    } catch (error) {
      console.error(`❌ Error executing ${toStage} automation:`, error);
      return { success: false, message: `Automation failed: ${error.message}` };
    }
  }

  /**
   * 📞 Stage 1: New Lead Automation
   */
  private static async executeNewLeadAutomation(lead: any, metadata?: any): Promise<{ success: boolean; message?: string }> {
    try {
      // Auto-assign sales rep based on region/skills
      if (!lead.assignedSalesRep) {
        const salesRep = await this.assignSalesRep(lead);
        if (salesRep) {
          await prisma.lead.update({
            where: { id: lead.id },
            data: { assignedSalesRep: salesRep.id },
          });
        }
      }

      // Send welcome email using email intelligence system
      await this.sendWelcomeEmail(lead);

      // Create customer record if doesn't exist
      if (!lead.customerId && lead.contactEmail) {
        const customer = await this.createCustomerFromLead(lead);
        if (customer) {
          await prisma.lead.update({
            where: { id: lead.id },
            data: { customerId: customer.id },
          });
        }
      }

      // Trigger AI lead scoring
      const aiInsights = await this.calculateAIInsights(lead);

      // Auto-advance if lead score is high enough
      if (aiInsights.leadScore >= 70) {
        // Schedule auto-advance to QUALIFIED stage
        setTimeout(async () => {
          await this.movePipelineCard(lead.id, 'NEW_LEAD', 'QUALIFIED', 'system');
        }, 5000); // 5 second delay for processing
      }

      return { success: true };
    } catch (error) {
      console.error('❌ New lead automation error:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * ✅ Stage 2: Qualified Lead Automation
   */
  private static async executeQualifiedAutomation(lead: any, metadata?: any): Promise<{ success: boolean; message?: string }> {
    try {
      // Collect basic data requirements
      const requiredFields = ['systemType', 'address', 'urgencyLevel'];
      const missingFields = requiredFields.filter(field => !metadata?.[field] && !(lead.metadata as any)?.[field]);

      if (missingFields.length > 0) {
        // Send data collection email/SMS
        await this.sendDataCollectionRequest(lead, missingFields);
      }

      // AI-powered lead scoring update
      const aiInsights = await this.calculateAIInsights(lead);

      // Update lead with AI insights
      await prisma.lead.update({
        where: { id: lead.id },
        data: {
          metadata: {
            ...(lead.metadata as any || {}),
            aiInsights,
            qualificationDate: new Date().toISOString(),
          },
        },
      });

      // Auto-advance if all data collected and score is good
      if (missingFields.length === 0 && aiInsights.leadScore >= 60) {
        // Trigger proposal preparation
        await this.triggerProposalPreparation(lead);
      }

      return { success: true };
    } catch (error) {
      console.error('❌ Qualified automation error:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 📄 Stage 3: Proposal Automation
   */
  private static async executeProposalAutomation(lead: any, metadata?: any): Promise<{ success: boolean; message?: string }> {
    try {
      // Auto-generate proposal draft
      const proposalDraft = await this.generateProposalDraft(lead);

      // Internal approval workflow
      if (lead.estimatedValue > 5000) {
        await this.triggerApprovalWorkflow(lead, proposalDraft);
      } else {
        // Auto-approve smaller proposals
        await this.approveAndSendProposal(lead, proposalDraft);
      }

      // Track proposal engagement
      await this.setupProposalTracking(lead);

      return { success: true };
    } catch (error) {
      console.error('❌ Proposal automation error:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 🤝 Stage 4: Negotiation/Scheduling Automation
   */
  private static async executeNegotiationAutomation(lead: any, metadata?: any): Promise<{ success: boolean; message?: string }> {
    try {
      // Show technician availability calendar
      const availableTechnicians = await this.getAvailableTechnicians(lead);

      // Auto-suggest optimal dates using availability algorithm
      const suggestedDates = await this.calculateOptimalScheduling(lead, availableTechnicians);

      // Send appointment options to customer
      await this.sendAppointmentOptions(lead, suggestedDates);

      // Set up appointment confirmation workflow
      await this.setupAppointmentConfirmation(lead);

      return { success: true };
    } catch (error) {
      console.error('❌ Negotiation automation error:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 🔧 Stage 5: In Progress Automation
   */
  private static async executeInProgressAutomation(lead: any, metadata?: any): Promise<{ success: boolean; message?: string }> {
    try {
      // Create service order if not exists
      if (!lead.serviceOrders || lead.serviceOrders.length === 0) {
        const serviceOrder = await this.createServiceOrderFromLead(lead);

        // Integrate with existing Kanban system
        await advancedKanbanService.createCard({
          title: lead.title,
          description: lead.description,
          customerId: lead.customerId,
          assignedTechnicianId: lead.assignedTechnician,
          serviceType: lead.serviceType,
          priority: lead.priority,
          stage: 'IN_PROGRESS',
          metadata: {
            leadId: lead.id,
            estimatedValue: lead.estimatedValue,
            ...metadata,
          },
        });
      }

      // Set up mobile app integration for technician reports
      await this.setupMobileReporting(lead);

      // Create QA check tasks
      await this.createQACheckTasks(lead);

      // Set up real-time progress updates
      await this.setupProgressTracking(lead);

      return { success: true };
    } catch (error) {
      console.error('❌ In progress automation error:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 💰 Stage 6: Closed Won Automation
   */
  private static async executeClosedWonAutomation(lead: any, metadata?: any): Promise<{ success: boolean; message?: string }> {
    try {
      // Auto-generate invoice via financial system integration
      const invoice = await this.generateInvoiceFromLead(lead);

      // Send invoice and payment reminders
      await this.sendInvoiceAndReminders(lead, invoice);

      // Update financial data in unified customer profile
      if (lead.customerId) {
        await this.updateUnifiedCustomerProfile(lead.customerId, {
          newInvoice: {
            id: invoice.id,
            amount: lead.estimatedValue,
            leadId: lead.id,
            timestamp: new Date(),
          },
        });
      }

      // Set up payment tracking
      await this.setupPaymentTracking(lead, invoice);

      return { success: true };
    } catch (error) {
      console.error('❌ Closed won automation error:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 🔄 Stage 7: Follow-up Automation
   */
  private static async executeFollowUpAutomation(lead: any, metadata?: any): Promise<{ success: boolean; message?: string }> {
    try {
      // Send satisfaction survey (NPS)
      await this.sendSatisfactionSurvey(lead);

      // Analyze upsell opportunities using AI insights
      const upsellOpportunities = await this.analyzeUpsellOpportunities(lead);

      // Auto-trigger nurturing campaigns
      await this.triggerNurturingCampaigns(lead, upsellOpportunities);

      // Update customer analytics and health scores
      if (lead.customerId) {
        await this.updateCustomerAnalytics(lead.customerId, {
          completedSale: {
            value: lead.estimatedValue,
            serviceType: lead.serviceType,
            satisfaction: metadata?.satisfactionScore,
            timestamp: new Date(),
          },
        });
      }

      // Schedule warranty service reminders
      await this.scheduleWarrantyReminders(lead);

      return { success: true };
    } catch (error) {
      console.error('❌ Follow-up automation error:', error);
      return { success: false, message: error.message };
    }
  }

  // ========================================
  // 🛠️ UTILITY METHODS
  // ========================================

  /**
   * Validate stage transition logic
   */
  private static validateStageTransition(from: SalesPipelineStage, to: SalesPipelineStage): boolean {
    const validTransitions: Record<SalesPipelineStage, SalesPipelineStage[]> = {
      NEW_LEAD: ['QUALIFIED', 'FOLLOW_UP'], // Can skip to follow-up if not qualified
      QUALIFIED: ['PROPOSAL', 'NEW_LEAD'], // Can go back to new lead
      PROPOSAL: ['NEGOTIATION', 'QUALIFIED'], // Can go back to qualified
      NEGOTIATION: ['IN_PROGRESS', 'PROPOSAL'], // Can go back to proposal
      IN_PROGRESS: ['CLOSED_WON', 'NEGOTIATION'], // Can go back to negotiation
      CLOSED_WON: ['FOLLOW_UP', 'IN_PROGRESS'], // Can go back to in progress
      FOLLOW_UP: ['NEW_LEAD'], // Can create new lead from follow-up
    };

    return validTransitions[from]?.includes(to) || false;
  }

  /**
   * Get default probability for each stage
   */
  private static getStageDefaultProbability(stage: SalesPipelineStage): number {
    const stageProbabilities: Record<SalesPipelineStage, number> = {
      NEW_LEAD: 10,
      QUALIFIED: 25,
      PROPOSAL: 50,
      NEGOTIATION: 75,
      IN_PROGRESS: 90,
      CLOSED_WON: 100,
      FOLLOW_UP: 100,
    };

    return stageProbabilities[stage] || 50;
  }

  /**
   * Calculate engagement level from communications
   */
  private static calculateEngagementLevel(communications: any[]): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (communications.length === 0) return 'LOW';

    const recentComms = communications.filter(c =>
      new Date(c.timestamp) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    );

    if (recentComms.length >= 5) return 'HIGH';
    if (recentComms.length >= 2) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Calculate sentiment score from communications
   */
  private static calculateSentimentScore(communications: any[]): number {
    if (communications.length === 0) return 0;

    const sentimentSum = communications.reduce((sum, comm) => {
      return sum + (comm.sentiment || 0);
    }, 0);

    return sentimentSum / communications.length;
  }

  /**
   * Calculate lead score using AI and historical data
   */
  private static async calculateLeadScore(analysisData: any): Promise<number> {
    try {
      // Use Bielik V3 for advanced lead scoring
      const prompt = `
        Analyze this lead data and provide a lead score (0-100):

        Lead Info: ${JSON.stringify(analysisData.leadInfo)}
        Customer History: ${JSON.stringify(analysisData.customerHistory)}
        Communication Data: ${JSON.stringify(analysisData.communicationData)}

        Consider factors like:
        - Lead source quality
        - Service type demand
        - Customer history and value
        - Communication responsiveness
        - Urgency level

        Return only a number between 0-100.
      `;

      const response = await bielikService.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.3,
        maxTokens: 50,
      });

      const score = parseInt(response.content.trim());
      return isNaN(score) ? 50 : Math.max(0, Math.min(100, score));

    } catch (error) {
      console.error('❌ Error calculating AI lead score:', error);
      // Fallback to basic scoring
      return this.calculateBasicLeadScore(analysisData);
    }
  }

  /**
   * Basic lead scoring fallback
   */
  private static calculateBasicLeadScore(analysisData: any): number {
    let score = 50; // Base score

    // Lead source scoring
    const sourceScores = {
      REFERRAL: 20,
      WEBSITE: 15,
      PHONE: 10,
      EMAIL: 8,
      SOCIAL_MEDIA: 5,
      WALK_IN: 3,
    };
    score += sourceScores[analysisData.leadInfo.source] || 0;

    // Service type scoring
    const serviceScores = {
      INSTALLATION: 15,
      REPAIR: 10,
      MAINTENANCE: 8,
      INSPECTION: 5,
    };
    score += serviceScores[analysisData.leadInfo.serviceType] || 0;

    // Value scoring
    if (analysisData.leadInfo.estimatedValue > 10000) score += 15;
    else if (analysisData.leadInfo.estimatedValue > 5000) score += 10;
    else if (analysisData.leadInfo.estimatedValue > 2000) score += 5;

    // Customer history scoring
    if (analysisData.customerHistory) {
      if (analysisData.customerHistory.totalRevenue > 20000) score += 20;
      if (analysisData.customerHistory.healthScore > 80) score += 10;
      if (analysisData.customerHistory.churnRisk < 30) score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Generate AI recommendations
   */
  private static async generateAIRecommendations(analysisData: any): Promise<string[]> {
    try {
      const prompt = `
        Based on this lead analysis data, provide 3-5 specific action recommendations:
        ${JSON.stringify(analysisData)}

        Return as a JSON array of strings.
      `;

      const response = await bielikService.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.5,
        maxTokens: 200,
      });

      const recommendations = JSON.parse(response.content);
      return Array.isArray(recommendations) ? recommendations : [];

    } catch (error) {
      console.error('❌ Error generating AI recommendations:', error);
      return [
        'Follow up within 24 hours',
        'Gather detailed requirements',
        'Schedule site visit if needed',
        'Prepare customized proposal',
      ];
    }
  }

  // ========================================
  // 🔗 INTEGRATION METHODS (Stub implementations for now)
  // ========================================

  private static async updateUnifiedCustomerProfile(customerId: string, data: any): Promise<void> {
    // Integration with UnifiedCustomerProfileService
    console.log(`🔄 Updating unified customer profile ${customerId} with:`, data);
    // Implementation would call UnifiedCustomerProfileService methods
  }

  private static async assignSalesRep(lead: any): Promise<any> {
    // Auto-assign based on region/skills
    console.log(`👤 Auto-assigning sales rep for lead ${lead.id}`);
    return { id: 'sales-rep-1', name: 'John Smith' };
  }

  private static async sendWelcomeEmail(lead: any): Promise<void> {
    // Integration with email intelligence system
    console.log(`📧 Sending welcome email to ${lead.contactEmail}`);
  }

  private static async createCustomerFromLead(lead: any): Promise<any> {
    // Create customer record from lead data
    console.log(`👤 Creating customer from lead ${lead.id}`);
    return await prisma.customer.create({
      data: {
        name: lead.contactName,
        email: lead.contactEmail,
        phone: lead.contactPhone,
        address: lead.contactAddress,
        type: 'RESIDENTIAL',
        source: lead.source,
      },
    });
  }

  private static async sendDataCollectionRequest(lead: any, missingFields: string[]): Promise<void> {
    console.log(`📋 Requesting data collection for ${lead.id}:`, missingFields);
  }

  private static async triggerProposalPreparation(lead: any): Promise<void> {
    console.log(`📄 Triggering proposal preparation for ${lead.id}`);
  }

  private static async generateProposalDraft(lead: any): Promise<any> {
    console.log(`📝 Generating proposal draft for ${lead.id}`);
    return { id: 'proposal-1', content: 'Proposal content...' };
  }

  private static async triggerApprovalWorkflow(lead: any, proposal: any): Promise<void> {
    console.log(`✅ Triggering approval workflow for ${lead.id}`);
  }

  private static async approveAndSendProposal(lead: any, proposal: any): Promise<void> {
    console.log(`📤 Auto-approving and sending proposal for ${lead.id}`);
  }

  private static async setupProposalTracking(lead: any): Promise<void> {
    console.log(`👁️ Setting up proposal tracking for ${lead.id}`);
  }

  private static async getAvailableTechnicians(lead: any): Promise<any[]> {
    console.log(`🔧 Getting available technicians for ${lead.id}`);
    return [];
  }

  private static async calculateOptimalScheduling(lead: any, technicians: any[]): Promise<any[]> {
    console.log(`📅 Calculating optimal scheduling for ${lead.id}`);
    return [];
  }

  private static async sendAppointmentOptions(lead: any, dates: any[]): Promise<void> {
    console.log(`📅 Sending appointment options for ${lead.id}`);
  }

  private static async setupAppointmentConfirmation(lead: any): Promise<void> {
    console.log(`✅ Setting up appointment confirmation for ${lead.id}`);
  }

  private static async createServiceOrderFromLead(lead: any): Promise<any> {
    console.log(`🔧 Creating service order from lead ${lead.id}`);
    return await prisma.serviceOrder.create({
      data: {
        title: lead.title,
        description: lead.description,
        customerId: lead.customerId,
        serviceType: lead.serviceType,
        priority: lead.priority,
        status: 'SCHEDULED',
        estimatedDuration: 4,
        metadata: { leadId: lead.id },
      },
    });
  }

  private static async setupMobileReporting(lead: any): Promise<void> {
    console.log(`📱 Setting up mobile reporting for ${lead.id}`);
  }

  private static async createQACheckTasks(lead: any): Promise<void> {
    console.log(`✅ Creating QA check tasks for ${lead.id}`);
  }

  private static async setupProgressTracking(lead: any): Promise<void> {
    console.log(`📊 Setting up progress tracking for ${lead.id}`);
  }

  private static async generateInvoiceFromLead(lead: any): Promise<any> {
    console.log(`💰 Generating invoice from lead ${lead.id}`);
    return { id: 'invoice-1', amount: lead.estimatedValue };
  }

  private static async sendInvoiceAndReminders(lead: any, invoice: any): Promise<void> {
    console.log(`📧 Sending invoice and reminders for ${lead.id}`);
  }

  private static async setupPaymentTracking(lead: any, invoice: any): Promise<void> {
    console.log(`💳 Setting up payment tracking for ${lead.id}`);
  }

  private static async sendSatisfactionSurvey(lead: any): Promise<void> {
    console.log(`📊 Sending satisfaction survey for ${lead.id}`);
  }

  private static async analyzeUpsellOpportunities(lead: any): Promise<any[]> {
    console.log(`📈 Analyzing upsell opportunities for ${lead.id}`);
    return [];
  }

  private static async triggerNurturingCampaigns(lead: any, opportunities: any[]): Promise<void> {
    console.log(`📧 Triggering nurturing campaigns for ${lead.id}`);
  }

  private static async updateCustomerAnalytics(customerId: string, data: any): Promise<void> {
    console.log(`📊 Updating customer analytics for ${customerId}`);
  }

  private static async scheduleWarrantyReminders(lead: any): Promise<void> {
    console.log(`🔔 Scheduling warranty reminders for ${lead.id}`);
  }

  private static async sendStageTransitionNotifications(cardId: string, fromStage: SalesPipelineStage, toStage: SalesPipelineStage, lead: any): Promise<void> {
    console.log(`🔔 Sending stage transition notifications: ${fromStage} → ${toStage}`);
  }

  private static async executePostTransitionAutomation(lead: any, toStage: SalesPipelineStage, metadata?: any): Promise<void> {
    console.log(`⚡ Executing post-transition automation for ${toStage}`);
  }

  private static calculateBasicChurnRisk(analysisData: any): number {
    // Basic churn risk calculation
    return 30; // Default 30% risk
  }

  private static calculateUpsellPotential(analysisData: any): number {
    // Basic upsell potential calculation
    return 40; // Default 40% potential
  }

  private static async calculateStageMetrics(stages: any): Promise<void> {
    // Calculate metrics for each stage
    console.log('📊 Calculating stage metrics');
  }

  private static async calculatePipelineAnalytics(cards: any[]): Promise<any> {
    // Calculate overall pipeline analytics
    const totalValue = cards.reduce((sum, card) => sum + card.estimatedValue, 0);
    const weightedValue = cards.reduce((sum, card) => sum + (card.estimatedValue * card.probability / 100), 0);

    return {
      totalPipelineValue: totalValue,
      weightedPipelineValue: weightedValue,
      averageDealSize: cards.length > 0 ? totalValue / cards.length : 0,
      conversionRates: {},
      averageSalesCycle: 30, // days
      topPerformers: [],
    };
  }
}

// Export singleton instance
export const enhancedSalesPipelineService = new EnhancedSalesPipelineService();

// Export convenience functions
export const getSalesPipelineBoard = (filters?: any) =>
  EnhancedSalesPipelineService.getSalesPipelineBoard(filters);

export const moveSalesPipelineCard = (cardId: string, fromStage: SalesPipelineStage, toStage: SalesPipelineStage, userId: string, metadata?: any) =>
  EnhancedSalesPipelineService.movePipelineCard(cardId, fromStage, toStage, userId, metadata);
